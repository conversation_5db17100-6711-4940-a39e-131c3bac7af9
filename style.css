        body {
            font-family: 'Inter', sans-serif;
            background-color: #020617; /* slate-950 */
            color: #d1d5db; /* gray-300 */
        }
        .hero-gradient {
            background: radial-gradient(ellipse at top, rgba(14, 165, 233, 0.15), transparent 60%);
        }
        .feature-card {
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(15, 23, 42, 0.5); /* slate-900 with opacity */
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        .feature-card:hover {
            border: 1px solid rgba(56, 189, 248, 0.5); /* sky-400 with opacity */
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(14, 165, 233, 0.1);
        }
        .pricing-card {
            border: 1px solid rgba(255, 255, 255, 0.1);
            background: #0f172a; /* slate-900 */
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
        }
        .pricing-card:hover {
            border-color: #38bdf8;
        }
        .pricing-card .card-content {
            flex-grow: 1;
        }
        .btn-primary {
            background-color: #0ea5e9; /* sky-500 */
            color: white;
            transition: background-color 0.3s ease;
        }
        .btn-primary:hover {
            background-color: #38bdf8; /* sky-400 */
        }
        .btn-secondary {
            background-color: transparent;
            border: 1px solid #38bdf8;
            color: #38bdf8;
            transition: all 0.3s ease;
        }
        .btn-secondary:hover {
            background-color: #38bdf8;
            color: white;
        }
        .faq-item {
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        .faq-item summary {
            cursor: pointer;
            outline: none;
        }
        .faq-item[open] summary {
           color: #38bdf8;
        }
        .server-tab {
            color: #9ca3af; /* gray-400 */
        }
        .server-tab.active {
            background-color: #0ea5e9; /* sky-500 */
            color: white;
        }
        .server-tab:hover {
            color: #38bdf8; /* sky-400 */
        }